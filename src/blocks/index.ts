import { ConcertArchiveBlockConfig } from "./concert-archive-block/concert-archive-block.config";
import { ConversionBlockConfig } from "./conversion-block/conversion-block.config";
import { ContentBlockConfig } from "./content-block/content-block.config";
import { ProgramArchiveBlockConfig } from "./program-archive-block/program-archive-block.config";
import { ComposerArchiveBlockConfig } from "./composer-archive-block/composer-archive-block.config";
import { PunkScrollerBlockConfig } from "./punk-scroller-block/punk-scroller-block.config";
import { ChoirDirectorBlockConfig } from "./choir-director-block/choir-director-block.config";
import { YoutubeEmbedBlockConfig } from "./youtube-embed-block/youtube-embed-block.config";
import { LongListBlockConfig } from "./long-list-block/long-list-block.config";
import { CriticArchiveBlockConfig } from "./critic-archive-block/critic-archive-block.config";

export const baseBlocks = [
	ProgramArchiveBlockConfig,
	PunkScrollerBlockConfig,
	CriticArchiveBlockConfig,
	YoutubeEmbedBlockConfig,
	LongListBlockConfig,
	ConversionBlockConfig,
	ConcertArchiveBlockConfig,
	ContentBlockConfig,
	ComposerArchiveBlockConfig,
	ChoirDirectorBlockConfig,
];
