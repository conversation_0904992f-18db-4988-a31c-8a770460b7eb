import type { <PERSON> } from "react";
import type { Page } from "@/payload-types";
import RichText from "@/components/render/render-rich-text";

export const AboutHero: FC<Page["hero"]> = ({ content, title, richText }) => {
	const sanitizedTitle =
		title?.replace(
			/Hard-Chor/g,
			'<span style="white-space: nowrap;">Hard-Chor</span>',
		) || "";

	return (
		<section className="layout-block pt-16">
			<div className="relative default-grid">
				<h1
					className="col-span-8"
					// biome-ignore lint/security/noDangerouslySetInnerHtml: <explanation>
					dangerouslySetInnerHTML={{ __html: sanitizedTitle }}
				/>
				<h3 className="col-span-8 indent-[var(--width-columns-2)]">
					{content}
				</h3>
				<div className="h-[17.5vw] relative col-span-full">
					<div className="absolute scale-[1.25] top-1/2 -translate-y-1/2 translate-x-1/9 w-screen top-">
						<img src="/swirl.svg" alt="swirl" className="w-full h-auto" />
					</div>
				</div>
				<div className="col-span-7 col-start-6">
					<RichText data={richText} />
				</div>
			</div>
		</section>
	);
};
