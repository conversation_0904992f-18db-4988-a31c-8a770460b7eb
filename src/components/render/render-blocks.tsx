import type React from "react";
import type { Page } from "@/payload-types";
import { Fragment } from "react";
import { ProgramArchiveBlock } from "@/blocks/program-archive-block/program-archive-block.block";
import { ConcertArchiveBlock } from "@/blocks/concert-archive-block/concert-archive-block.block";
import { ContentBlock } from "@/blocks/content-block/content-block.block";
import { ConversionBlock } from "@/blocks/conversion-block/conversion-block.block";
import { ComposerArchiveBlock } from "@/blocks/composer-archive-block/composer-archive-block.block";
import { PunkScrollerBlock } from "@/blocks/punk-scroller-block/punk-scroller-block.block";
import { ChoirDirectorBlock } from "@/blocks/choir-director-block/choir-director-block.block";
import { YoutubeEmbedBlock } from "@/blocks/youtube-embed-block/youtube-embed-block.block";
import { LongListBlock } from "@/blocks/long-list-block/long-list-block.block";
import { CriticArchiveBlock } from "@/blocks/critic-archive-block/critic-archive-block.block";

const blockComponents = {
	"program-archive": ProgramArchive<PERSON><PERSON>,
	conversion: ConversionB<PERSON>,
	"punk-scroller": <PERSON><PERSON><PERSON><PERSON>er<PERSON><PERSON>,
	"composer-archive": ComposerArchiveBlock,
	"concert-archive": ConcertArchiveBlock,
	"critic-archive": CriticArchiveBlock,
	"choir-director": ChoirDirectorBlock,
	"youtube-embed": YoutubeEmbedBlock,
	"long-list": LongListBlock,
	content: ContentBlock,
};

export const RenderBlocks: React.FC<{
	blocks: Page["layout"][0][];
}> = (props) => {
	const { blocks } = props;

	const hasBlocks = blocks && Array.isArray(blocks) && blocks.length > 0;

	if (hasBlocks) {
		return blocks.map((block, index) => {
			const { blockType } = block;

			if (blockType && blockType in blockComponents) {
				const Block = blockComponents[blockType];

				if (Block) {
					return (
						<Fragment key={block.id}>
							{/* @ts-expect-error - ts is not happy with the block types cause all blocks have different props */}
							<Block {...block} />
						</Fragment>
					);
				}
			}
			return null;
		});
	}

	return null;
};
