import type { GlobalConfig } from "payload";
import { link } from "@/fields/link";

export const Header: GlobalConfig = {
	slug: "header",
	label: "Header",
	access: {
		read: () => true,
	},
	fields: [
		{
			name: "navItems",
			label: "Navigationselemente",
			type: "array",
			maxRows: 6,
			admin: {
				initCollapsed: true,
				components: {
					RowLabel: "@/globals/RowLabel#RowLabel",
				},
			},
			fields: [
				{
					type: "row",
					fields: [
						{
							name: "isMegaNav",
							label: "Gibts Unterseiten?",
							type: "checkbox",
							defaultValue: false,
						},
					],
				},
				link({
					appearances: false,
					overrides: {
						admin: {
							condition: (_, siblingData) => siblingData.isMegaNav !== true,
						},
					},
				}),
				{
					name: "label",
					type: "text",
					label: "Label",
					admin: {
						condition: (_, siblingData) => siblingData.isMegaNav === true,
					},
				},
				{
					name: "children",
					label: "Unterseiten",
					type: "array",
					admin: {
						condition: (_, siblingData) => siblingData.isMegaNav === true,
					},
					fields: [link({ appearances: false })],
				},
			],
		},
	],
};
