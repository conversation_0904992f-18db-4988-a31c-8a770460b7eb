"use client";

// import "@/css/index.css";
import { ThemeColorsView } from "./views/theme-colors-view";
import { ColorPickerView } from "./views/color-picker-view";
import { useState } from "react";

type DropdownColorPickerProps = {
	fontColor?: string;
	onFontColorChange: (color: string, cssVariableColor?: string) => void;
	onApplyStyles: () => void;
};

export type ColorSpectrum = "hex" | "hsl" | "rgb";

const defaultColor = "#000";

export const ColorPicker = ({
	fontColor = defaultColor,
	onFontColorChange,
	onApplyStyles,
}: DropdownColorPickerProps) => {
	return (
		<div className="flex">
			<ColorPickerView
				onApplyStyles={onApplyStyles}
				fontColor={fontColor}
				onFontColorChange={onFontColorChange}
			/>
		</div>
	);
};
