import React, { useCallback, useEffect, useRef, useState } from "react";

import appTheme from "@/css/colors";
import { createSentenceFromCamelCase } from "../../utils/create-sentence-from-camel-case";
import { translateColor } from "../../utils/translate-color";

type ColorSpectrum = "hex" | "hsl" | "rgb";

type Props = {
	onFontColorChange: (color: string, cssVariableColor: string) => void;
	onColorSpectrumChange: (colorSpectrum: ColorSpectrum) => void;
	colorSpectrum: ColorSpectrum;
};

export const ThemeColorsView = ({
	onFontColorChange,
	onColorSpectrumChange,
	colorSpectrum,
}: Props) => {
	return (
		<div>
			<RadioGroupList
				value={colorSpectrum}
				onValueChange={onColorSpectrumChange}
			/>
			<div className="h-2 w-full " />
			<div className="h-[265px] overflow-auto">
				<div className="flex flex-col gap-2">
					{Object.entries(appTheme).map(([key, variable]) => {
						return (
							<ThemeColorButton
								colorSpectrum={colorSpectrum}
								variableName={key}
								key={key}
								onFontColorChange={onFontColorChange}
								variable={variable}
							/>
						);
					})}
				</div>
			</div>
		</div>
	);
};

type BtnProps = {
	variableName: string;
	variable: string;
	onFontColorChange: (color: string, cssVariableColor: string) => void;
	colorSpectrum: ColorSpectrum;
};

const ThemeColorButton = ({
	variableName,
	variable,
	onFontColorChange,
	colorSpectrum,
}: BtnProps) => {
	const colorRef = useRef(null);
	const [backgroundColor, setBackgroundColor] = React.useState<
		string | undefined
	>(undefined);

	const getTranslateSpectrum = useCallback(
		(colorSpectrum: ColorSpectrum): "HEX" | "RGBstring" | "HSLstring" => {
			switch (colorSpectrum) {
				case "hex":
					return "HEX";
				case "hsl":
					return "HSLstring";
				case "rgb":
					return "RGBstring";
				default:
					return "HEX";
			}
		},
		[],
	);

	useEffect(() => {
		if (colorRef.current) {
			const computedStyle = getComputedStyle(colorRef.current);
			setBackgroundColor(
				translateColor(
					computedStyle.backgroundColor,
					getTranslateSpectrum(colorSpectrum),
					0,
				),
			);
		}
	}, [colorSpectrum, getTranslateSpectrum]);

	return (
		<button
			type="button"
			key={variableName}
			onClick={() => {
				if (!backgroundColor) return;
				onFontColorChange(
					translateColor(backgroundColor, "HEX"),
					`hsl(var(${variable}))`,
				);
			}}
			className="border-none outline-none flex gap-2 items-center cursor-pointer p-1 rounded-md bg-[var(--theme-elevation-0)] hover:bg-[var(--theme-elevation-50)]"
		>
			<div className="flex items-center w-full gap-2">
				<div
					style={{ backgroundColor: `hsl(var(${variable}))` }}
					ref={colorRef}
					className={
						"h-9 w-9 rounded-full border-white border-[1px] border-solid"
					}
				/>
				<div className="leading-none">
					{createSentenceFromCamelCase(variableName, 15)}
				</div>
			</div>
			<div className="leading-none whitespace-nowrap bg-[var(--theme-elevation-150)] mr-2 p-2 rounded-sm">
				{backgroundColor}
			</div>
		</button>
	);
};

type RadioGroupListProps = {
	value: ColorSpectrum;
	onValueChange: (value: ColorSpectrum) => void;
};
const RadioGroupList = ({ onValueChange, value }: RadioGroupListProps) => {
	return (
		<div className="flex p-2 rounded-md w-fit bg-[var(--theme-elevation-50)]">
			<div className="flex items-center gap-1">
				<input
					type="radio"
					name="color-type"
					value="hex"
					id="hex"
					checked={value === "hex"}
					onChange={(e) => {
						onValueChange(e.target.value as ColorSpectrum);
					}}
				/>
				<label htmlFor="hex">HEX</label>
			</div>
			<div className="flex items-center gap-1">
				<input
					type="radio"
					name="color-type"
					value="hsl"
					id="hsl"
					checked={value === "hsl"}
					onChange={(e) => {
						onValueChange(e.target.value as ColorSpectrum);
					}}
				/>
				<label htmlFor="hsl">HSL</label>
			</div>

			<div className="flex items-center gap-1">
				<input
					type="radio"
					name="color-type"
					value="rgb"
					id="rgb"
					checked={value === "rgb"}
					onChange={(e) => {
						onValueChange(e.target.value as ColorSpectrum);
					}}
				/>
				<label htmlFor="rgb">RGB</label>
			</div>
		</div>
	);
};
